//
//  DanmakuGifCell.swift
//  DanmuKitMac
//
//  Created by Augment Agent on 2025/8/22.
//  Ported from DanmakuKit iOS
//

import Cocoa

public class DanmakuGifCell: DanmakuCell {
    
    private var gifAnimator: GifAnimator?
    private var currentImage: CGImage?
    
    public override func willDisplay() {
        super.willDisplay()
        setupGifAnimation()
    }
    
    public override func displaying(_ context: CGContext, _ size: CGSize, _ isCancelled: Bool) {
        guard !isCancelled else { return }
        
        if let image = currentImage {
            context.draw(image, in: CGRect(origin: .zero, size: size))
        }
    }
    
    public override func didDisplay(_ finished: Bool) {
        super.didDisplay(finished)
        if finished {
            gifAnimator?.startAnimating()
        }
    }
    
    public override func enterTrack() {
        super.enterTrack()
        gifAnimator?.startAnimating()
    }
    
    public override func leaveTrack() {
        super.leaveTrack()
        gifAnimator?.stopAnimating()
        gifAnimator = nil
    }
    
    private func setupGifAnimation() {
        guard let model = model as? DanmakuGifCellModel,
              let gifData = model.gifData else {
            return
        }
        
        gifAnimator = GifAnimator(data: gifData)
        currentImage = gifAnimator?.currentFrame
        
        gifAnimator?.frameUpdateHandler = { [weak self] image in
            guard let self = self else { return }
            self.currentImage = image
            self.redraw()
        }
    }
    
    deinit {
        gifAnimator?.stopAnimating()
    }
    
}
