//
//  DanmakuGifCellModel.swift
//  DanmuKitMac
//
//  Created by Augment Agent on 2025/8/22.
//  Ported from DanmakuKit iOS
//

import Cocoa

public class DanmakuGifCellModel: DanmakuCellModel {
    
    public var identifier: String = ""
    
    public var gifData: Data?
    
    public var size: CGSize = .zero
    
    public var track: UInt?
    
    public var displayTime: Double = 8.0
    
    public var type: DanmakuCellType = .floating
    
    public var cellClass: DanmakuCell.Type {
        return DanmakuGifCell.self
    }
    
    public init() {}
    
    public func isEqual(to cellModel: DanmakuCellModel) -> Bool {
        return identifier == cellModel.identifier
    }
    
    public func calculateSize() {
        guard let data = gifData,
              let imageSource = CGImageSourceCreateWithData(data as CFData, nil),
              CGImageSourceGetCount(imageSource) > 0,
              let firstImage = CGImageSourceCreateImageAtIndex(imageSource, 0, nil) else {
            size = CGSize(width: 100, height: 100) // Default size
            return
        }
        
        size = CGSize(width: firstImage.width, height: firstImage.height)
    }
    
}
