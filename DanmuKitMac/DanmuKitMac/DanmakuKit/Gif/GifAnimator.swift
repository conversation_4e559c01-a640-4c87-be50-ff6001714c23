//
//  GifAnimator.swift
//  DanmuKitMac
//
//  Created by Augment Agent on 2025/8/22.
//  Ported from DanmakuKit iOS
//

import Cocoa
import ImageIO

public class GifAnimator {
    
    private let imageSource: CGImageSource
    private let frameCount: Int
    private let frameDurations: [TimeInterval]
    private var currentFrameIndex = 0
    private var displayLink: CVDisplayLink?
    private var lastFrameTime: CFTimeInterval = 0
    private var accumulatedTime: TimeInterval = 0
    
    public var currentFrame: CGImage? {
        guard currentFrameIndex < frameCount else { return nil }
        return CGImageSourceCreateImageAtIndex(imageSource, currentFrameIndex, nil)
    }
    
    public var isAnimating: Bool {
        return displayLink != nil
    }
    
    public var frameUpdateHandler: ((CGImage?) -> Void)?
    
    public init?(data: Data) {
        guard let imageSource = CGImageSourceCreateWithData(data as CFData, nil) else {
            return nil
        }
        
        self.imageSource = imageSource
        self.frameCount = CGImageSourceGetCount(imageSource)
        
        var durations: [TimeInterval] = []
        for i in 0..<frameCount {
            let frameDuration = GifAnimator.frameDuration(at: i, source: imageSource)
            durations.append(frameDuration)
        }
        self.frameDurations = durations
    }
    
    public func startAnimating() {
        guard !isAnimating else { return }
        
        var displayLink: CVDisplayLink?
        let result = CVDisplayLinkCreateWithActiveCGDisplays(&displayLink)
        
        guard result == kCVReturnSuccess, let link = displayLink else {
            return
        }
        
        CVDisplayLinkSetOutputCallback(link, { (displayLink, inNow, inOutputTime, flagsIn, flagsOut, displayLinkContext) -> CVReturn in
            let animator = Unmanaged<GifAnimator>.fromOpaque(displayLinkContext!).takeUnretainedValue()
            animator.updateFrame(timestamp: CFTimeInterval(inNow.pointee.videoTime) / CFTimeInterval(inNow.pointee.videoTimeScale))
            return kCVReturnSuccess
        }, Unmanaged.passUnretained(self).toOpaque())
        
        CVDisplayLinkStart(link)
        self.displayLink = link
        lastFrameTime = CACurrentMediaTime()
    }
    
    public func stopAnimating() {
        guard let displayLink = displayLink else { return }
        CVDisplayLinkStop(displayLink)
        self.displayLink = nil
        currentFrameIndex = 0
        accumulatedTime = 0
    }
    
    private func updateFrame(timestamp: CFTimeInterval) {
        let deltaTime = timestamp - lastFrameTime
        lastFrameTime = timestamp
        accumulatedTime += deltaTime
        
        let currentFrameDuration = frameDurations[currentFrameIndex]
        
        if accumulatedTime >= currentFrameDuration {
            accumulatedTime -= currentFrameDuration
            currentFrameIndex = (currentFrameIndex + 1) % frameCount
            
            DispatchQueue.main.async { [weak self] in
                self?.frameUpdateHandler?(self?.currentFrame)
            }
        }
    }
    
    private static func frameDuration(at index: Int, source: CGImageSource) -> TimeInterval {
        guard let properties = CGImageSourceCopyPropertiesAtIndex(source, index, nil) as? [String: Any],
              let gifProperties = properties[kCGImagePropertyGIFDictionary as String] as? [String: Any] else {
            return 0.1 // Default duration
        }
        
        let delayTime = gifProperties[kCGImagePropertyGIFDelayTime as String] as? TimeInterval ??
                       gifProperties[kCGImagePropertyGIFUnclampedDelayTime as String] as? TimeInterval ??
                       0.1
        
        return max(delayTime, 0.02) // Minimum 20ms per frame
    }
    
    deinit {
        stopAnimating()
    }
    
}
