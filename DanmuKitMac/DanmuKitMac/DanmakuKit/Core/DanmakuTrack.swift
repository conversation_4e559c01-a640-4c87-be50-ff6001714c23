//
//  DanmakuTrack.swift
//  DanmuKitMac
//
//  Created by Augment Agent on 2025/8/22.
//  Ported from DanmakuKit iOS
//

import Cocoa

protocol DanmakuTrack {
    
    var positionY: CGFloat { get set }
    
    var index: UInt { get set }
    
    var stopClosure: ((_ cell: DanmakuCell) -> Void)? { get set }
    
    var danmakuCount: Int { get }
    
    var isOverlap: Bool { get set }
    
    var playingSpeed: Float { get set }
    
    init(view: NSView)
    
    func shoot(danmaku: DanmakuCell)
    
    func canShoot(danmaku: DanmakuCellModel) -> Bool
    
    func play()
    
    func pause()
    
    func stop()
    
    func pause(_ danmaku: DanmakuCellModel) -> Bool
    
    func play(_ danmaku: DanmakuCellModel) -> Bool
    
    func sync(_ danmaku: DanmakuCell, at progress: Float)
    
    func syncAndPlay(_ danmaku: DanmakuCell, at progress: Float)
    
    func canSync(_ danmaku: DanmakuCellModel, at progress: Float) -> Bool
    
    func clean()
    
}

class FloatingDanmakuTrack: DanmakuTrack {
    
    var positionY: CGFloat = 0
    
    var index: UInt = 0
    
    var stopClosure: ((DanmakuCell) -> Void)?
    
    var danmakuCount: Int {
        return cells.count
    }
    
    var isOverlap = false
    
    var playingSpeed: Float = 1.0
    
    private weak var view: NSView?
    
    private var cells: [DanmakuCell] = []
    
    required init(view: NSView) {
        self.view = view
    }
    
    func shoot(danmaku: DanmakuCell) {
        guard let model = danmaku.model else { return }
        let totalWidth = view!.frame.width + danmaku.bounds.width
        let startFrame = CGRect(x: view!.frame.width, y: positionY - danmaku.bounds.height / 2.0, width: danmaku.bounds.width, height: danmaku.bounds.height)
        cells.append(danmaku)
        danmaku.layer?.opacity = 1
        danmaku.frame = startFrame
        danmaku.model?.track = index
        danmaku.animationTime = model.displayTime
        danmaku.enterTrack()
        addAnimation(to: danmaku)
    }
    
    func canShoot(danmaku: DanmakuCellModel) -> Bool {
        if isOverlap { return true }
        
        guard let lastDanmaku = cells.last else { return true }
        guard let lastModel = lastDanmaku.model else { return true }
        
        let lastDanmakuRightEdge = lastDanmaku.realFrame.maxX
        let viewWidth = view?.frame.width ?? 0
        
        if lastDanmakuRightEdge <= viewWidth {
            return true
        }
        
        let lastDanmakuSpeed = (viewWidth + lastDanmaku.bounds.width) / CGFloat(lastModel.displayTime)
        let currentDanmakuSpeed = (viewWidth + danmaku.size.width) / CGFloat(danmaku.displayTime)
        
        if currentDanmakuSpeed <= lastDanmakuSpeed {
            return true
        }
        
        let timeToReachRightEdge = (lastDanmakuRightEdge - viewWidth) / (currentDanmakuSpeed - lastDanmakuSpeed)
        let lastDanmakuPositionAtThatTime = lastDanmakuRightEdge - lastDanmakuSpeed * timeToReachRightEdge
        
        return lastDanmakuPositionAtThatTime + lastDanmaku.bounds.width <= viewWidth
    }
    
    private func addAnimation(to danmaku: DanmakuCell) {
        guard let model = danmaku.model else { return }
        
        let animation = CABasicAnimation(keyPath: "position.x")
        animation.fromValue = danmaku.layer?.position.x
        animation.toValue = -(danmaku.bounds.width / 2.0)
        animation.duration = model.displayTime / Double(playingSpeed)
        animation.timingFunction = CAMediaTimingFunction(name: .linear)
        animation.isRemovedOnCompletion = false
        animation.fillMode = .forwards
        
        animation.delegate = AnimationDelegate { [weak self, weak danmaku] finished in
            guard let self = self, let danmaku = danmaku else { return }
            if finished {
                danmaku.leaveTrack()
                self.stopClosure?(danmaku)
                if let index = self.cells.firstIndex(of: danmaku) {
                    self.cells.remove(at: index)
                }
            }
        }
        
        danmaku.layer?.add(animation, forKey: "floating")
    }
    
    func play() {
        cells.forEach { cell in
            if let layer = cell.layer {
                layer.speed = 1.0
                layer.timeOffset = 0
            }
        }
    }
    
    func pause() {
        cells.forEach { cell in
            if let layer = cell.layer {
                let pausedTime = layer.convertTime(CACurrentMediaTime(), from: nil)
                layer.speed = 0.0
                layer.timeOffset = pausedTime
            }
        }
    }
    
    func stop() {
        cells.forEach {
            $0.removeFromSuperview()
            $0.layer?.removeAllAnimations()
        }
        cells.removeAll()
    }
    
    func pause(_ danmaku: DanmakuCellModel) -> Bool {
        guard let cell = cells.first(where: { $0.model?.identifier == danmaku.identifier }) else { return false }
        if let layer = cell.layer {
            let pausedTime = layer.convertTime(CACurrentMediaTime(), from: nil)
            layer.speed = 0.0
            layer.timeOffset = pausedTime
        }
        return true
    }
    
    func play(_ danmaku: DanmakuCellModel) -> Bool {
        guard let cell = cells.first(where: { $0.model?.identifier == danmaku.identifier }) else { return false }
        if let layer = cell.layer {
            layer.speed = 1.0
            layer.timeOffset = 0
        }
        return true
    }
    
    func sync(_ danmaku: DanmakuCell, at progress: Float) {
        guard let model = danmaku.model else { return }
        let totalWidth = view!.frame.width + danmaku.bounds.width
        let syncFrame = CGRect(x: view!.frame.width - totalWidth * CGFloat(progress), y: positionY - danmaku.bounds.height / 2.0, width: danmaku.bounds.width, height: danmaku.bounds.height)
        cells.append(danmaku)
        danmaku.layer?.opacity = 1
        danmaku.frame = syncFrame
        danmaku.model?.track = index
        danmaku.animationTime = model.displayTime * Double(progress)
    }
    
    func syncAndPlay(_ danmaku: DanmakuCell, at progress: Float) {
        sync(danmaku, at: progress)
        addAnimation(to: danmaku)
    }
    
    func canSync(_ danmaku: DanmakuCellModel, at progress: Float) -> Bool {
        return canShoot(danmaku: danmaku)
    }
    
    func clean() {
        stop()
    }
    
}

class TopDanmakuTrack: DanmakuTrack {

    var positionY: CGFloat = 0

    var index: UInt = 0

    var stopClosure: ((DanmakuCell) -> Void)?

    var danmakuCount: Int {
        return cells.count
    }

    var isOverlap = false

    var playingSpeed: Float = 1.0

    private weak var view: NSView?

    private var cells: [DanmakuCell] = []

    required init(view: NSView) {
        self.view = view
    }

    func shoot(danmaku: DanmakuCell) {
        guard let model = danmaku.model else { return }
        cells.append(danmaku)
        danmaku.animationTime = model.displayTime
        danmaku.model?.track = index
        let originX = (view!.bounds.width - danmaku.bounds.width) / 2.0
        let originY = positionY - danmaku.bounds.height / 2.0
        danmaku.frame = CGRect(x: originX, y: originY, width: danmaku.bounds.width, height: danmaku.bounds.height)
        danmaku.layer?.opacity = 1
        danmaku.enterTrack()
        addAnimation(to: danmaku)
    }

    func canShoot(danmaku: DanmakuCellModel) -> Bool {
        return cells.isEmpty
    }

    private func addAnimation(to danmaku: DanmakuCell) {
        guard let model = danmaku.model else { return }

        let animation = CABasicAnimation(keyPath: "opacity")
        animation.fromValue = 1.0
        animation.toValue = 0.0
        animation.duration = model.displayTime / Double(playingSpeed)
        animation.beginTime = CACurrentMediaTime() + model.displayTime * 0.8 / Double(playingSpeed)
        animation.timingFunction = CAMediaTimingFunction(name: .easeIn)
        animation.isRemovedOnCompletion = false
        animation.fillMode = .forwards

        animation.delegate = AnimationDelegate { [weak self, weak danmaku] finished in
            guard let self = self, let danmaku = danmaku else { return }
            if finished {
                danmaku.leaveTrack()
                self.stopClosure?(danmaku)
                if let index = self.cells.firstIndex(of: danmaku) {
                    self.cells.remove(at: index)
                }
            }
        }

        danmaku.layer?.add(animation, forKey: "top")
    }

    func play() {
        cells.forEach { cell in
            if let layer = cell.layer {
                layer.speed = 1.0
                layer.timeOffset = 0
            }
        }
    }

    func pause() {
        cells.forEach { cell in
            if let layer = cell.layer {
                let pausedTime = layer.convertTime(CACurrentMediaTime(), from: nil)
                layer.speed = 0.0
                layer.timeOffset = pausedTime
            }
        }
    }

    func stop() {
        cells.forEach {
            $0.removeFromSuperview()
            $0.layer?.removeAllAnimations()
        }
        cells.removeAll()
    }

    func pause(_ danmaku: DanmakuCellModel) -> Bool {
        guard let cell = cells.first(where: { $0.model?.identifier == danmaku.identifier }) else { return false }
        if let layer = cell.layer {
            let pausedTime = layer.convertTime(CACurrentMediaTime(), from: nil)
            layer.speed = 0.0
            layer.timeOffset = pausedTime
        }
        return true
    }

    func play(_ danmaku: DanmakuCellModel) -> Bool {
        guard let cell = cells.first(where: { $0.model?.identifier == danmaku.identifier }) else { return false }
        if let layer = cell.layer {
            layer.speed = 1.0
            layer.timeOffset = 0
        }
        return true
    }

    func sync(_ danmaku: DanmakuCell, at progress: Float) {
        guard let model = danmaku.model else { return }
        cells.append(danmaku)
        danmaku.animationTime = model.displayTime * Double(progress)
        danmaku.model?.track = index
        let originX = (view!.bounds.width - danmaku.bounds.width) / 2.0
        let originY = positionY - danmaku.bounds.height / 2.0
        danmaku.frame = CGRect(x: originX, y: originY, width: danmaku.bounds.width, height: danmaku.bounds.height)
        danmaku.layer?.opacity = 1
    }

    func syncAndPlay(_ danmaku: DanmakuCell, at progress: Float) {
        sync(danmaku, at: progress)
        addAnimation(to: danmaku)
    }

    func canSync(_ danmaku: DanmakuCellModel, at progress: Float) -> Bool {
        return cells.isEmpty
    }

    func clean() {
        stop()
    }

}

class BottomDanmakuTrack: DanmakuTrack {

    var positionY: CGFloat = 0

    var index: UInt = 0

    var stopClosure: ((DanmakuCell) -> Void)?

    var danmakuCount: Int {
        return cells.count
    }

    var isOverlap = false

    var playingSpeed: Float = 1.0

    private weak var view: NSView?

    private var cells: [DanmakuCell] = []

    required init(view: NSView) {
        self.view = view
    }

    func shoot(danmaku: DanmakuCell) {
        guard let model = danmaku.model else { return }
        cells.append(danmaku)
        danmaku.animationTime = model.displayTime
        danmaku.model?.track = index
        let originX_b = (view!.bounds.width - danmaku.bounds.width) / 2.0
        let originY_b = positionY - danmaku.bounds.height / 2.0
        danmaku.frame = CGRect(x: originX_b, y: originY_b, width: danmaku.bounds.width, height: danmaku.bounds.height)
        danmaku.layer?.opacity = 1
        danmaku.enterTrack()
        addAnimation(to: danmaku)
    }

    func canShoot(danmaku: DanmakuCellModel) -> Bool {
        return cells.isEmpty
    }

    private func addAnimation(to danmaku: DanmakuCell) {
        guard let model = danmaku.model else { return }

        let animation = CABasicAnimation(keyPath: "opacity")
        animation.fromValue = 1.0
        animation.toValue = 0.0
        animation.duration = model.displayTime / Double(playingSpeed)
        animation.beginTime = CACurrentMediaTime() + model.displayTime * 0.8 / Double(playingSpeed)
        animation.timingFunction = CAMediaTimingFunction(name: .easeIn)
        animation.isRemovedOnCompletion = false
        animation.fillMode = .forwards

        animation.delegate = AnimationDelegate { [weak self, weak danmaku] finished in
            guard let self = self, let danmaku = danmaku else { return }
            if finished {
                danmaku.leaveTrack()
                self.stopClosure?(danmaku)
                if let index = self.cells.firstIndex(of: danmaku) {
                    self.cells.remove(at: index)
                }
            }
        }

        danmaku.layer?.add(animation, forKey: "bottom")
    }

    func play() {
        cells.forEach { cell in
            if let layer = cell.layer {
                layer.speed = 1.0
                layer.timeOffset = 0
            }
        }
    }

    func pause() {
        cells.forEach { cell in
            if let layer = cell.layer {
                let pausedTime = layer.convertTime(CACurrentMediaTime(), from: nil)
                layer.speed = 0.0
                layer.timeOffset = pausedTime
            }
        }
    }

    func stop() {
        cells.forEach {
            $0.removeFromSuperview()
            $0.layer?.removeAllAnimations()
        }
        cells.removeAll()
    }

    func pause(_ danmaku: DanmakuCellModel) -> Bool {
        guard let cell = cells.first(where: { $0.model?.identifier == danmaku.identifier }) else { return false }
        if let layer = cell.layer {
            let pausedTime = layer.convertTime(CACurrentMediaTime(), from: nil)
            layer.speed = 0.0
            layer.timeOffset = pausedTime
        }
        return true
    }

    func play(_ danmaku: DanmakuCellModel) -> Bool {
        guard let cell = cells.first(where: { $0.model?.identifier == danmaku.identifier }) else { return false }
        if let layer = cell.layer {
            layer.speed = 1.0
            layer.timeOffset = 0
        }
        return true
    }

    func sync(_ danmaku: DanmakuCell, at progress: Float) {
        guard let model = danmaku.model else { return }
        cells.append(danmaku)
        danmaku.animationTime = model.displayTime * Double(progress)
        danmaku.model?.track = index
        let originX_b = (view!.bounds.width - danmaku.bounds.width) / 2.0
        let originY_b = positionY - danmaku.bounds.height / 2.0
        danmaku.frame = CGRect(x: originX_b, y: originY_b, width: danmaku.bounds.width, height: danmaku.bounds.height)
        danmaku.layer?.opacity = 1
    }

    func syncAndPlay(_ danmaku: DanmakuCell, at progress: Float) {
        sync(danmaku, at: progress)
        addAnimation(to: danmaku)
    }

    func canSync(_ danmaku: DanmakuCellModel, at progress: Float) -> Bool {
        return cells.isEmpty
    }

    func clean() {
        stop()
    }

}

// Animation delegate helper
class AnimationDelegate: NSObject, CAAnimationDelegate {
    private let completion: (Bool) -> Void

    init(completion: @escaping (Bool) -> Void) {
        self.completion = completion
        super.init()
    }

    func animationDidStop(_ anim: CAAnimation, finished flag: Bool) {
        completion(flag)
    }
}
