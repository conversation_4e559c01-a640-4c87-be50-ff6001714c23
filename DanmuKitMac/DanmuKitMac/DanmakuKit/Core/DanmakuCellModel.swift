//
//  DanmakuCellModel.swift
//  DanmuKitMac
//
//  Created by Augment Agent on 2025/8/22.
//  Ported from DanmakuKit iOS
//

import Cocoa

public enum DanmakuCellType {
    case floating
    case top
    case bottom
}

public protocol DanmakuCellModel {
    
    var cellClass: <PERSON>makuCell.Type { get }
    
    var size: CGSize { get }
    
    /// Track for danmaku
    var track: UInt? { get set }
    
    var displayTime: Double { get }
    
    var type: DanmakuCellType { get }
    
    /// unique identifier
    var identifier: String { get }
    
    /// Used to determine if two cellmodels are equal
    /// - Parameter cellModel: other cellModel
    func isEqual(to cellModel: DanmakuCellModel) -> Bool
    
}
