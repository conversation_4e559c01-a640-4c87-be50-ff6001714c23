//
//  DanmakuView.swift
//  DanmuKitMac
//
//  Created by Augment Agent on 2025/8/22.
//  Ported from DanmakuKit iOS
//

import Cocoa

public protocol DanmakuViewDelegate: AnyObject {
    
    /// A  danmaku is about to be reused and cellModel is set for you before calling this method.
    /// - Parameters:
    ///   - danmakuView: view of the danmaku
    ///   - danmaku: danmaku
    func danmakuView(_ danmakuView: DanmakuView, dequeueReusable danmaku: DanmakuCell)
    
    ///  This method is called when the danmaku has no space to display.
    /// - Parameters:
    ///   - danmakuView: view of the danmaku
    ///   - danmaku:  cellModel of danmaku
    func danmakuView(_ danmakuView: DanmakuView, noSpaceShoot danmaku: DanmakuCellModel)
    
    ///  This method is called when the danmaku is about to be displayed.
    /// - Parameters:
    ///   - danmakuView: view of the danmaku
    ///   - danmaku:  danmaku
    func danmakuView(_ danmakuView: DanmakuView, willDisplay danmaku: DanmakuCell)
    
    /// This method is called when the danmaku is about to end.
    /// - Parameters:
    ///   - danmakuView: view of the danmaku
    ///   - danmaku: danmaku
    func danmakuView(_ danmakuView: DanmakuView, didEndDisplaying danmaku: DanmakuCell)
    
    /// This method is called when danmaku is clicked.
    /// - Parameters:
    ///   - danmakuView: view of the danmaku
    ///   - danmaku: danmaku
    func danmakuView(_ danmakuView: DanmakuView, didClicked danmaku: DanmakuCell)
    
    ///  This method is called when the danmaku has no space to sync display.
    /// - Parameters:
    ///   - danmakuView: view of the danmaku
    ///   - danmaku:  cellModel of danmaku
    func danmakuView(_ danmakuView: DanmakuView, noSpaceSync danmaku: DanmakuCellModel)
    
}

public extension DanmakuViewDelegate {
    
    func danmakuView(_ danmakuView: DanmakuView, dequeueReusable danmaku: DanmakuCell) {}
    
    func danmakuView(_ danmakuView: DanmakuView, noSpaceShoot danmaku: DanmakuCellModel) {}
    
    func danmakuView(_ danmakuView: DanmakuView, willDisplay danmaku: DanmakuCell) {}
    
    func danmakuView(_ danmakuView: DanmakuView, didEndDisplaying danmaku: DanmakuCell) {}
    
    func danmakuView(_ danmakuView: DanmakuView, didClicked danmaku: DanmakuCell) {}
    
    func danmakuView(_ danmakuView: DanmakuView, noSpaceSync danmaku: DanmakuCellModel) {}
    
}

public enum DanmakuStatus {
    case play
    case pause
    case stop
}

public class DanmakuView: NSView {
    
    public weak var delegate: DanmakuViewDelegate?
    
    /// If this property is false, the danmaku will not be reused and danmakuView(_:dequeueReusable danmaku:) methods will not be called.
    public var enableCellReusable = false
    
    /// Each danmaku is in one track and the number of tracks in the view depends on the height of the track.
    public var trackHeight: CGFloat = 20 {
        didSet {
            guard oldValue != trackHeight else { return }
            recalculateTracks()
        }
    }
    
    /// Padding of top area, the actual offset of the top danmaku will refer to this property.
    public var paddingTop: CGFloat = 0 {
        didSet {
            guard oldValue != paddingTop else { return }
            recalculateTracks()
        }
    }
    
    /// Padding of bottom area, the actual offset of the bottom danmaku will refer to this property.
    public var paddingBottom: CGFloat = 0 {
        didSet {
            guard oldValue != paddingBottom else { return }
            recalculateTracks()
        }
    }
    
    /// Enable floating danmaku
    public var enableFloatingDanmaku = true
    
    /// Enable top danmaku
    public var enableTopDanmaku = true
    
    /// Enable bottom danmaku
    public var enableBottomDanmaku = true
    
    /// Whether danmaku can overlap
    public var isOverlap = false {
        didSet {
            floatingTracks.forEach { $0.isOverlap = isOverlap }
            topTracks.forEach { $0.isOverlap = isOverlap }
            bottomTracks.forEach { $0.isOverlap = isOverlap }
        }
    }
    
    /// Playing speed
    public var playingSpeed: Float = 1.0 {
        didSet {
            floatingTracks.forEach { $0.playingSpeed = playingSpeed }
            topTracks.forEach { $0.playingSpeed = playingSpeed }
            bottomTracks.forEach { $0.playingSpeed = playingSpeed }
        }
    }
    
    /// Current status
    public private(set) var status: DanmakuStatus = .stop
    
    private var floatingTracks: [FloatingDanmakuTrack] = []
    private var topTracks: [TopDanmakuTrack] = []
    private var bottomTracks: [BottomDanmakuTrack] = []
    
    private var cellPool: [String: [DanmakuCell]] = [:]
    
    public override var isFlipped: Bool { true }

    public override init(frame frameRect: NSRect) {
        super.init(frame: frameRect)
        setupView()
    }

    public required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupView()
    }
    
    deinit {
        stop()
    }
    
    private func setupView() {
        wantsLayer = true
        recalculateTracks()
    }
    
    public override func layout() {
        super.layout()
        recalculateTracks()
    }
    
    /// Start playing danmaku
    public func play() {
        guard status != .play else { return }
        status = .play
        floatingTracks.forEach { $0.play() }
        topTracks.forEach { $0.play() }
        bottomTracks.forEach { $0.play() }
    }
    
    /// Pause danmaku
    public func pause() {
        guard status == .play else { return }
        status = .pause
        floatingTracks.forEach { $0.pause() }
        topTracks.forEach { $0.pause() }
        bottomTracks.forEach { $0.pause() }
    }
    
    /// Stop danmaku and clean resources
    public func stop() {
        status = .stop
        floatingTracks.forEach { $0.stop() }
        topTracks.forEach { $0.stop() }
        bottomTracks.forEach { $0.stop() }
        cellPool.removeAll()
    }
    
    /// Shoot a danmaku
    public func shoot(danmaku: DanmakuCellModel) {
        guard status == .play else { return }
        switch danmaku.type {
        case .floating:
            guard enableFloatingDanmaku else { return }
            guard !floatingTracks.isEmpty else { return }
        case .top:
            guard enableTopDanmaku else { return }
            guard !topTracks.isEmpty else { return }
        case .bottom:
            guard enableBottomDanmaku else { return }
            guard !bottomTracks.isEmpty else { return }
        }
        
        guard let cell = obtainCell(with: danmaku) else { return }
        
        let shootTrack: DanmakuTrack
        if isOverlap {
            shootTrack = findLeastNumberDanmakuTrack(for: danmaku)
        } else {
            guard let t = findSuitableTrack(for: danmaku) else {
                delegate?.danmakuView(self, noSpaceShoot: danmaku)
                if enableCellReusable {
                    appendCellToPool(cell)
                }
                return
            }
            shootTrack = t
        }
        
        if cell.superview == nil {
            addSubview(cell)
        }
        
        delegate?.danmakuView(self, willDisplay: cell)
        cell.layer?.setNeedsDisplay()
        shootTrack.shoot(danmaku: cell)
    }
    
    /// Check if can shoot danmaku
    public func canShoot(danmaku: DanmakuCellModel) -> Bool {
        guard status == .play else { return false }
        switch danmaku.type {
        case .floating:
            guard enableFloatingDanmaku else { return false }
            return (floatingTracks.first { (t) -> Bool in
                return t.canShoot(danmaku: danmaku)
            }) != nil
        case .top:
            guard enableTopDanmaku else { return false }
            return (topTracks.first { (t) -> Bool in
                return t.canShoot(danmaku: danmaku)
            }) != nil
        case .bottom:
            guard enableBottomDanmaku else { return false }
            return (bottomTracks.first { (t) -> Bool in
                return t.canShoot(danmaku: danmaku)
            }) != nil
        }
    }
    
    /// Clean all displayed danmaku
    public func clean() {
        floatingTracks.forEach { $0.clean() }
        bottomTracks.forEach { $0.clean() }
        topTracks.forEach { $0.clean() }
    }
    
    /// Recalculate tracks based on current frame
    public func recalculateTracks() {
        guard bounds.height > 0 else { return }
        
        let availableHeight = bounds.height - paddingTop - paddingBottom
        let trackCount = Int(availableHeight / trackHeight)
        
        // Update floating tracks
        updateTracks(&floatingTracks, count: trackCount, type: FloatingDanmakuTrack.self)
        
        // Update top tracks  
        updateTracks(&topTracks, count: trackCount / 3, type: TopDanmakuTrack.self)
        
        // Update bottom tracks
        updateTracks(&bottomTracks, count: trackCount / 3, type: BottomDanmakuTrack.self)
        
        // Set positions
        for (index, track) in floatingTracks.enumerated() {
            track.positionY = paddingTop + trackHeight * CGFloat(index) + trackHeight / 2
            track.index = UInt(index)
        }
        
        for (index, track) in topTracks.enumerated() {
            track.positionY = paddingTop + trackHeight * CGFloat(index) + trackHeight / 2
            track.index = UInt(index)
        }
        
        for (index, track) in bottomTracks.enumerated() {
            // For macOS, calculate bottom tracks from the top (since we flipped coordinates)
            track.positionY = bounds.height - paddingBottom - trackHeight * CGFloat(index + 1) + trackHeight / 2
            track.index = UInt(index)
        }
    }

    private func updateTracks<T: DanmakuTrack>(_ tracks: inout [T], count: Int, type: T.Type) {
        let currentCount = tracks.count

        if currentCount < count {
            // Add new tracks
            for _ in currentCount..<count {
                var track = type.init(view: self)
                track.isOverlap = isOverlap
                track.playingSpeed = playingSpeed
                track.stopClosure = { [weak self] cell in
                    self?.handleDanmakuStop(cell)
                }
                tracks.append(track)
            }
        } else if currentCount > count {
            // Remove excess tracks
            let tracksToRemove = tracks.suffix(currentCount - count)
            tracksToRemove.forEach { $0.clean() }
            tracks.removeLast(currentCount - count)
        }
    }

    private func handleDanmakuStop(_ cell: DanmakuCell) {
        delegate?.danmakuView(self, didEndDisplaying: cell)
        cell.removeFromSuperview()
        if enableCellReusable {
            appendCellToPool(cell)
        }
    }

    private func findSuitableTrack(for danmaku: DanmakuCellModel) -> DanmakuTrack? {
        switch danmaku.type {
        case .floating:
            return floatingTracks.first { $0.canShoot(danmaku: danmaku) }
        case .top:
            return topTracks.first { $0.canShoot(danmaku: danmaku) }
        case .bottom:
            return bottomTracks.first { $0.canShoot(danmaku: danmaku) }
        }
    }

    private func findLeastNumberDanmakuTrack(for danmaku: DanmakuCellModel) -> DanmakuTrack {
        switch danmaku.type {
        case .floating:
            return floatingTracks.min { $0.danmakuCount < $1.danmakuCount } ?? floatingTracks[0]
        case .top:
            return topTracks.min { $0.danmakuCount < $1.danmakuCount } ?? topTracks[0]
        case .bottom:
            return bottomTracks.min { $0.danmakuCount < $1.danmakuCount } ?? bottomTracks[0]
        }
    }

    private func obtainCell(with danmaku: DanmakuCellModel) -> DanmakuCell? {
        var cell: DanmakuCell?
        if enableCellReusable {
            cell = cellFromPool(danmaku)
        }

        let frame = CGRect(x: bounds.width, y: 0, width: danmaku.size.width, height: danmaku.size.height)
        if cell == nil {
            guard let cls = NSClassFromString(NSStringFromClass(danmaku.cellClass)) as? DanmakuCell.Type else {
                assert(false, "Launched Danmaku must inherit from DanmakuCell!")
                return nil
            }
            cell = cls.init(frame: frame)
            cell?.model = danmaku
            let click = NSClickGestureRecognizer(target: self, action: #selector(danmakuDidClick(_:)))
            cell?.addGestureRecognizer(click)
        } else {
            cell?.frame = frame
            cell?.model = danmaku
            delegate?.danmakuView(self, dequeueReusable: cell!)
        }
        return cell
    }

    @objc private func danmakuDidClick(_ gesture: NSClickGestureRecognizer) {
        guard let cell = gesture.view as? DanmakuCell else { return }
        delegate?.danmakuView(self, didClicked: cell)
    }

    private func cellFromPool(_ danmaku: DanmakuCellModel) -> DanmakuCell? {
        let className = NSStringFromClass(danmaku.cellClass)
        guard let cells = cellPool[className], !cells.isEmpty else { return nil }
        let cell = cells.last!
        cellPool[className]?.removeLast()
        return cell
    }

    private func appendCellToPool(_ cell: DanmakuCell) {
        let className = NSStringFromClass(type(of: cell))
        if cellPool[className] == nil {
            cellPool[className] = []
        }
        cellPool[className]?.append(cell)
    }

    /// Sync display danmaku at specific progress
    public func sync(danmaku: DanmakuCellModel, at progress: Float) {
        guard status != .stop else { return }
        assert(progress <= 1.0, "Cannot sync danmaku at progress \(progress).")
        switch danmaku.type {
        case .floating:
            guard enableFloatingDanmaku else { return }
            guard !floatingTracks.isEmpty else { return }
        case .top:
            guard enableTopDanmaku else { return }
            guard !topTracks.isEmpty else { return }
        case .bottom:
            guard enableBottomDanmaku else { return }
            guard !bottomTracks.isEmpty else { return }
        }
        guard let cell = obtainCell(with: danmaku) else { return }

        let syncTrack: DanmakuTrack
        if isOverlap {
            syncTrack = findLeastNumberDanmakuTrack(for: danmaku)
        } else {
            guard let t = findSuitableSyncTrack(for: danmaku, at: progress) else {
                delegate?.danmakuView(self, noSpaceSync: danmaku)
                return
            }
            syncTrack = t
        }

        if cell.superview == nil {
            addSubview(cell)
        }

        delegate?.danmakuView(self, willDisplay: cell)
        cell.layer?.setNeedsDisplay()
        if status == .play {
            syncTrack.syncAndPlay(cell, at: progress)
        } else {
            syncTrack.sync(cell, at: progress)
        }
    }

    private func findSuitableSyncTrack(for danmaku: DanmakuCellModel, at progress: Float) -> DanmakuTrack? {
        switch danmaku.type {
        case .floating:
            return floatingTracks.first { $0.canSync(danmaku, at: progress) }
        case .top:
            return topTracks.first { $0.canSync(danmaku, at: progress) }
        case .bottom:
            return bottomTracks.first { $0.canSync(danmaku, at: progress) }
        }
    }

}
