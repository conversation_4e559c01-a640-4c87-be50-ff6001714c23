//
//  DanmakuAsyncLayer.swift
//  DanmuKitMac
//
//  Created by Augment Agent on 2025/8/22.
//  Ported from DanmakuKit iOS
//

import Cocoa

class Sentinel {

    private var value: Int32 = 0

    public func getValue() -> Int32 {
        return value
    }

    public func increase() {
        _ = OSAtomicIncrement32(&value)
    }

}

public class DanmakuAsyncLayer: CALayer {
    
    /// When true, it is drawn asynchronously and is true by default.
    public var displayAsync = true
    
    public var willDisplay: ((_ layer: DanmakuAsyncLayer) -> Void)?
    
    public var displaying: ((_ context: CGContext, _ size: CGSize, _ isCancelled:(() -> Bool)) -> Void)?
    
    public var didDisplay: ((_ layer: Dan<PERSON>kuAsyncLayer, _ finished: Bool) -> Void)?
    
    /// The number of queues to draw the danmaku.
    public static var drawDanmakuQueueCount = 16 {
        didSet {
            guard drawDanmakuQueueCount != oldValue else { return }
            pool = nil
            createPoolIfNeed()
        }
    }
    
    private let sentinel = Sentinel()
    
    private static var pool: DanmakuQueuePool?
    
    private static func createPoolIfNeed() {
        guard pool == nil else { return }
        pool = DanmakuQueuePool(name: "DanmakuAsyncLayer.pool", queueCount: drawDanmakuQueueCount, qos: .default)
    }
    
    public override func setNeedsDisplay() {
        cancelAsyncDisplay()
        super.setNeedsDisplay()
    }
    
    public override func display() {
        super.contents = super.contents
        displayAsync(displayAsync)
    }
    
    private func displayAsync(_ async: Bool) {
        willDisplay?(self)
        
        let size = bounds.size
        let isOpaque = self.isOpaque
        var backgroundColor = self.backgroundColor
        
        if size.width < 1 || size.height < 1 {
            contents = nil
            didDisplay?(self, true)
            return
        }
        
        let opaque = isOpaque
        let scale = contentsScale
        let colorSpace = CGColorSpaceCreateDeviceRGB()
        let alphaInfo: CGImageAlphaInfo = opaque ? .noneSkipLast : .premultipliedLast
        
        if async {
            let sentinelValue = sentinel.getValue()
            let isCancelled = { [weak self] in
                return self?.sentinel.getValue() != sentinelValue
            }
            
            Self.createPoolIfNeed()
            Self.pool?.queue.async { [weak self] in
                guard let self = self else { return }
                if isCancelled() { return }
                
                let context = CGContext(data: nil, width: Int(size.width * scale), height: Int(size.height * scale), bitsPerComponent: 8, bytesPerRow: 0, space: colorSpace, bitmapInfo: alphaInfo.rawValue)
                
                if let context = context {
                    context.scaleBy(x: scale, y: scale)

                    // Fill background if opaque
                    if opaque {
                        context.saveGState()
                        if backgroundColor == nil || (backgroundColor?.alpha ?? 0) < 1 {
                            context.setFillColor(NSColor.white.cgColor)
                            context.fill(CGRect(origin: .zero, size: size))
                        }
                        if let backgroundColor = backgroundColor {
                            context.setFillColor(backgroundColor)
                            context.fill(CGRect(origin: .zero, size: size))
                        }
                        context.restoreGState()
                    }

                    self.displaying?(context, size, isCancelled)
                    
                    if isCancelled() {
                        DispatchQueue.main.async {
                            self.didDisplay?(self, false)
                        }
                        return
                    }
                    
                    let cgImage = context.makeImage()
                    DispatchQueue.main.async {
                        if isCancelled() {
                            self.didDisplay?(self, false)
                            return
                        }
                        self.contents = cgImage
                        self.didDisplay?(self, true)
                    }
                }
            }
        } else {
            let isCancelled = { false }
            let context = CGContext(data: nil, width: Int(size.width * scale), height: Int(size.height * scale), bitsPerComponent: 8, bytesPerRow: 0, space: colorSpace, bitmapInfo: alphaInfo.rawValue)
            
            if let context = context {
                context.scaleBy(x: scale, y: scale)

                // Fill background if opaque
                if opaque {
                    context.saveGState()
                    if backgroundColor == nil || (backgroundColor?.alpha ?? 0) < 1 {
                        context.setFillColor(NSColor.white.cgColor)
                        context.fill(CGRect(origin: .zero, size: size))
                    }
                    if let backgroundColor = backgroundColor {
                        context.setFillColor(backgroundColor)
                        context.fill(CGRect(origin: .zero, size: size))
                    }
                    context.restoreGState()
                }

                displaying?(context, size, isCancelled)
                let cgImage = context.makeImage()
                contents = cgImage
                didDisplay?(self, true)
            }
        }
    }
    
    private func cancelAsyncDisplay() {
        sentinel.increase()
    }
    
}
