//
//  DanmakuViewAdapter.swift
//  DanmuKitMac
//
//  Created by Augment Agent on 2025/8/22.
//  Ported from DanmakuKit iOS
//

import SwiftUI

@available(macOS 10.15, *)
public struct DanmakuViewAdapter: NSViewRepresentable {
    
    public typealias NSViewType = DanmakuView
    
    @ObservedObject var coordinator: Coordinator
    
    public init(coordinator: Coordinator) {
        self.coordinator = coordinator
    }
    
    public func makeNSView(context: Context) -> NSViewType {
        return coordinator.makeView()
    }
    
    public func updateNSView(_ nsView: NSViewType, context: Context) {}
    
    public func makeCoordinator() -> Coordinator {
        return coordinator
    }
    
    public class Coordinator: ObservableObject {
        
        public init() {}
        
        public private(set) var danmakuView: DanmakuView?
        
        private var frameObserver: Any?
        
        public weak var danmakuViewDelegate: DanmakuViewDelegate? {
            willSet {
                danmakuView?.delegate = newValue
            }
        }
        
        public func play() {
            danmakuView?.play()
        }
        
        public func pause() {
            danmakuView?.pause()
        }
        
        public func stop() {
            danmakuView?.stop()
        }
        
        public func clean() {
            danmakuView?.clean()
        }
        
        public func shoot(danmaku: DanmakuCellModel) {
            danmakuView?.shoot(danmaku: danmaku)
        }
        
        public func canShoot(danmaku: DanmakuCellModel) -> Bool {
            guard let view = danmakuView else { return false }
            return view.canShoot(danmaku: danmaku)
        }
        
        public func recalculateTracks() {
            danmakuView?.recalculateTracks()
        }
        
        public func sync(danmaku: DanmakuCellModel, at progress: Float) {
            danmakuView?.sync(danmaku: danmaku, at: progress)
        }
        
        func makeView() -> DanmakuView {
            danmakuView = DanmakuView(frame: .zero)
            // Note: NSView doesn't have a publisher for frame changes like UIView
            // We could use KVO or other observation methods if needed
            return danmakuView!
        }
        
    }
    
}
