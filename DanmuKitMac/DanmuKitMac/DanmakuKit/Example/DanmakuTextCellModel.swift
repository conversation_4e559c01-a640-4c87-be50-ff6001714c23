//
//  DanmakuTextCellModel.swift
//  DanmuKitMac
//
//  Created by Augment Agent on 2025/8/22.
//  Example text danmaku model
//

import Cocoa

public class DanmakuTextCellModel: DanmakuCellModel {
    
    public var identifier: String = ""
    
    public var text: String = ""
    
    public var font: NSFont = NSFont.systemFont(ofSize: 15)
    
    public var textColor: NSColor = NSColor.white
    
    public var strokeColor: NSColor = NSColor.black
    
    public var strokeWidth: CGFloat = 1.0
    
    public var size: CGSize = .zero
    
    public var track: UInt?
    
    public var displayTime: Double = 8.0
    
    public var type: DanmakuCellType = .floating
    
    public var cellClass: DanmakuCell.Type {
        return DanmakuTextCell.self
    }
    
    public init() {}
    
    public init(text: String) {
        self.text = text
        self.identifier = UUID().uuidString
        calculateSize()
    }
    
    public func calculateSize() {
        let attributes: [NSAttributedString.Key: Any] = [
            .font: font
        ]
        
        let attributedString = NSAttributedString(string: text, attributes: attributes)
        let boundingRect = attributedString.boundingRect(
            with: CGSize(width: CGFloat.greatestFiniteMagnitude, height: CGFloat.greatestFiniteMagnitude),
            options: [.usesLineFragmentOrigin, .usesFontLeading]
        )
        
        size = CGSize(width: ceil(boundingRect.width) + strokeWidth * 2, 
                     height: ceil(boundingRect.height) + strokeWidth * 2)
    }
    
    public func isEqual(to cellModel: DanmakuCellModel) -> Bool {
        return identifier == cellModel.identifier
    }
    
}
